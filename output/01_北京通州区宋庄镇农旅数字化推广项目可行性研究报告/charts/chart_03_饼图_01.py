#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
from matplotlib import font_manager

# 字体设置（复用模板）
font_list = font_manager.findSystemFonts(fontpaths=None, fontext='ttf')
alibaba_fonts = [f for f in font_list if 'Alibaba' in f]
alibaba_font = None
if alibaba_fonts:
    try:
        alibaba_font = font_manager.FontProperties(fname=alibaba_fonts[0])
    except:
        pass

for font_path in alibaba_fonts:
    try:
        font_manager.fontManager.addfont(font_path)
    except:
        pass

plt.rcParams['font.sans-serif'] = ['AlibabaPuHuiTi-3-55-Regular', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

if not alibaba_font:
    try:
        alibaba_font = font_manager.FontProperties(family='SimHei')
    except:
        pass

# 创建宋庄镇土地资源利用结构数据
labels = ['耕地', '建设用地', '生态用地', '水域', '道路用地', '其他用地']
sizes = [37.5, 25.2, 20.8, 8.3, 5.7, 2.5]  # 百分比
colors = ['#8FBC8F', '#CD853F', '#90EE90', '#87CEEB', '#D3D3D3', '#F0E68C']

plt.figure(figsize=(12, 8))
wedges, texts, autotexts = plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%',
                                  startangle=90, explode=(0.05, 0, 0, 0, 0, 0), shadow=True)

for autotext in autotexts:
    autotext.set_color('white')
    autotext.set_fontweight('bold')

for text in texts:
    if alibaba_font:
        text.set_fontproperties(alibaba_font)

if alibaba_font:
    plt.title('宋庄镇土地资源利用结构\n（总面积40平方公里）', 
              fontsize=16, fontweight='bold', pad=20, fontproperties=alibaba_font)
else:
    plt.title('宋庄镇土地资源利用结构\n（总面积40平方公里）', 
              fontsize=16, fontweight='bold', pad=20)

plt.axis('equal')
plt.legend(wedges, [f'{label}\n{size}%' for label, size in zip(labels, sizes)],
          title="土地类型及占比", loc="center left", bbox_to_anchor=(1, 0, 0.5, 1),
          prop=alibaba_font if alibaba_font else None)

plt.tight_layout()
plt.figtext(0.02, 0.02, '数据来源：宋庄镇土地利用现状调查', fontsize=8, 
           fontproperties=alibaba_font if alibaba_font else None, alpha=0.7)

output_dir = os.path.dirname(os.path.abspath(__file__))
output_path = os.path.join(output_dir, 'chart_03_饼图_01.png')
plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
print(f"图表已保存至: {output_path}")
plt.close()
