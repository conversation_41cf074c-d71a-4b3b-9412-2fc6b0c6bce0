#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
from matplotlib import font_manager

# 查找阿里巴巴字体
font_list = font_manager.findSystemFonts(fontpaths=None, fontext='ttf')
alibaba_fonts = [f for f in font_list if 'Alibaba' in f]

# 创建字体对象
alibaba_font = None
if alibaba_fonts:
    try:
        alibaba_font = font_manager.FontProperties(fname=alibaba_fonts[0])
    except Exception as e:
        print(f"创建字体对象失败: {e}")

# 手动添加阿里巴巴字体
for font_path in alibaba_fonts:
    try:
        font_manager.fontManager.addfont(font_path)
    except Exception as e:
        print(f"添加字体失败: {font_path}, 错误: {e}")

# 设置字体
plt.rcParams['font.sans-serif'] = ['AlibabaPuHuiTi-3-55-Regular', 'AlibabaPuHuiTi-3-65-Medium', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 如果没有成功创建字体对象，尝试使用名称创建
if not alibaba_font:
    try:
        alibaba_font = font_manager.FontProperties(family='AlibabaPuHuiTi-3-55-Regular')
    except Exception as e:
        try:
            alibaba_font = font_manager.FontProperties(family='SimHei')
        except:
            print("无法创建任何中文字体对象，图表中文可能无法正确显示")

# 设置全局字体样式
plt.rcParams['font.family'] = 'sans-serif'

# 创建输出目录
output_dir = os.path.dirname(os.path.abspath(__file__))
os.makedirs(output_dir, exist_ok=True)

# 设置政府报告专业配色方案
colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']

# 创建北京城区居民周末休闲需求分布数据
labels = ['亲子农事体验', '文化创意活动', '健康饮食体验', '自然观光游览', 
          '休闲放松活动', '社交互动体验', '其他需求']
sizes = [28.5, 22.3, 18.7, 15.2, 8.9, 4.8, 1.6]  # 百分比

# 创建图表
plt.figure(figsize=(12, 8))

# 突出显示前三位需求
explode = (0.1, 0.05, 0.05, 0, 0, 0, 0)

# 绘制饼图
wedges, texts, autotexts = plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%',
                                  startangle=90, explode=explode, shadow=True,
                                  textprops={'fontproperties': alibaba_font if alibaba_font else None})

# 设置百分比文字样式
for autotext in autotexts:
    autotext.set_color('white')
    autotext.set_fontweight('bold')
    autotext.set_fontsize(10)

# 设置标签文字样式
for text in texts:
    text.set_fontsize(11)
    if alibaba_font:
        text.set_fontproperties(alibaba_font)

# 设置图表标题
if alibaba_font:
    plt.title('北京城区居民周末休闲需求分布\n（基于1000份问卷调查）', 
              fontsize=16, fontweight='bold', pad=20, fontproperties=alibaba_font)
else:
    plt.title('北京城区居民周末休闲需求分布\n（基于1000份问卷调查）', 
              fontsize=16, fontweight='bold', pad=20)

# 确保饼图为圆形
plt.axis('equal')

# 添加图例
plt.legend(wedges, [f'{label}\n{size}%' for label, size in zip(labels, sizes)],
          title="需求类型及占比", loc="center left", bbox_to_anchor=(1, 0, 0.5, 1),
          prop=alibaba_font if alibaba_font else None)

# 设置布局
plt.tight_layout()

# 添加数据来源说明
plt.figtext(0.02, 0.02, '数据来源：2025年北京市居民休闲消费调查报告', fontsize=8, 
           fontproperties=alibaba_font if alibaba_font else None, alpha=0.7)

# 保存图表
output_path = os.path.join(output_dir, 'chart_01_饼图_01.png')
plt.savefig(output_path, dpi=300, bbox_inches='tight', 
           facecolor='white', edgecolor='none')
print(f"图表已保存至: {output_path}")

# 显示图表（可选）
# plt.show()

plt.close()
