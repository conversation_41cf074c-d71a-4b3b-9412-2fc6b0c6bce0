#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
from matplotlib import font_manager

# 查找阿里巴巴字体
font_list = font_manager.findSystemFonts(fontpaths=None, fontext='ttf')
alibaba_fonts = [f for f in font_list if 'Alibaba' in f]

# 创建字体对象
alibaba_font = None
if alibaba_fonts:
    try:
        alibaba_font = font_manager.FontProperties(fname=alibaba_fonts[0])
    except Exception as e:
        print(f"创建字体对象失败: {e}")

# 手动添加阿里巴巴字体
for font_path in alibaba_fonts:
    try:
        font_manager.fontManager.addfont(font_path)
    except Exception as e:
        print(f"添加字体失败: {font_path}, 错误: {e}")

# 设置字体
plt.rcParams['font.sans-serif'] = ['AlibabaPuHuiTi-3-55-Regular', 'AlibabaPuHuiTi-3-65-Medium', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 如果没有成功创建字体对象，尝试使用名称创建
if not alibaba_font:
    try:
        alibaba_font = font_manager.FontProperties(family='AlibabaPuHuiTi-3-55-Regular')
    except Exception as e:
        try:
            alibaba_font = font_manager.FontProperties(family='SimHei')
        except:
            print("无法创建任何中文字体对象，图表中文可能无法正确显示")

# 设置全局字体样式
plt.rcParams['font.family'] = 'sans-serif'

# 创建输出目录
output_dir = os.path.dirname(os.path.abspath(__file__))
os.makedirs(output_dir, exist_ok=True)

# 创建宋庄镇发展痛点分析数据
pain_points = {
    '农业规模小': {'impact': 8.5, 'difficulty': 6.2, 'category': '农业发展'},
    '农业效益低': {'impact': 9.0, 'difficulty': 7.1, 'category': '农业发展'},
    '农产品品牌弱': {'impact': 7.8, 'difficulty': 5.5, 'category': '农业发展'},
    '艺术资源整合不足': {'impact': 8.2, 'difficulty': 6.8, 'category': '艺术文化'},
    '艺术品牌建设不足': {'impact': 7.5, 'difficulty': 5.2, 'category': '艺术文化'},
    '数字化基础设施不足': {'impact': 9.2, 'difficulty': 8.5, 'category': '数字化发展'},
    '数字化技术应用不足': {'impact': 8.8, 'difficulty': 7.8, 'category': '数字化发展'},
    '数字化人才缺乏': {'impact': 9.5, 'difficulty': 8.2, 'category': '数字化发展'},
    '农旅吸引力不足': {'impact': 8.0, 'difficulty': 6.5, 'category': '农旅发展'},
    '体验项目单一': {'impact': 7.2, 'difficulty': 4.8, 'category': '农旅发展'},
    '服务质量不高': {'impact': 7.8, 'difficulty': 5.5, 'category': '农旅发展'},
    '资源整合不足': {'impact': 8.5, 'difficulty': 7.2, 'category': '农旅发展'}
}

# 转换为DataFrame
df = pd.DataFrame.from_dict(pain_points, orient='index')
df.reset_index(inplace=True)
df.rename(columns={'index': 'pain_point'}, inplace=True)

# 定义颜色映射
category_colors = {
    '农业发展': '#FF6B6B',
    '艺术文化': '#4ECDC4', 
    '数字化发展': '#45B7D1',
    '农旅发展': '#96CEB4'
}

# 创建图表
plt.figure(figsize=(12, 8))

# 绘制散点图
for category in df['category'].unique():
    category_data = df[df['category'] == category]
    plt.scatter(category_data['difficulty'], category_data['impact'], 
               c=category_colors[category], s=150, alpha=0.7, 
               edgecolors='white', linewidth=2, label=category)

# 添加痛点标签
for idx, row in df.iterrows():
    plt.annotate(row['pain_point'], 
                (row['difficulty'], row['impact']),
                xytext=(5, 5), textcoords='offset points',
                fontsize=9, fontproperties=alibaba_font if alibaba_font else None,
                bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.7))

# 添加象限分割线
plt.axhline(y=8, color='gray', linestyle='--', alpha=0.5)
plt.axvline(x=6.5, color='gray', linestyle='--', alpha=0.5)

# 添加象限标签
if alibaba_font:
    plt.text(3, 9.5, '高影响\n低难度', ha='center', va='center', fontsize=11,
            fontweight='bold', color='green', fontproperties=alibaba_font,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen', alpha=0.3))
    plt.text(8.5, 9.5, '高影响\n高难度', ha='center', va='center', fontsize=11,
            fontweight='bold', color='red', fontproperties=alibaba_font,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightcoral', alpha=0.3))
    plt.text(3, 6.5, '低影响\n低难度', ha='center', va='center', fontsize=11,
            fontweight='bold', color='blue', fontproperties=alibaba_font,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.3))
    plt.text(8.5, 6.5, '低影响\n高难度', ha='center', va='center', fontsize=11,
            fontweight='bold', color='orange', fontproperties=alibaba_font,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightyellow', alpha=0.3))
else:
    plt.text(3, 9.5, '高影响\n低难度', ha='center', va='center', fontsize=11,
            fontweight='bold', color='green',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen', alpha=0.3))
    plt.text(8.5, 9.5, '高影响\n高难度', ha='center', va='center', fontsize=11,
            fontweight='bold', color='red',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightcoral', alpha=0.3))
    plt.text(3, 6.5, '低影响\n低难度', ha='center', va='center', fontsize=11,
            fontweight='bold', color='blue',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.3))
    plt.text(8.5, 6.5, '低影响\n高难度', ha='center', va='center', fontsize=11,
            fontweight='bold', color='orange',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightyellow', alpha=0.3))

# 设置图表标题和标签
if alibaba_font:
    plt.title('宋庄镇发展痛点分析矩阵', fontsize=16, fontweight='bold', 
              pad=20, fontproperties=alibaba_font)
    plt.xlabel('解决难度（分）', fontsize=12, fontproperties=alibaba_font)
    plt.ylabel('影响程度（分）', fontsize=12, fontproperties=alibaba_font)
else:
    plt.title('宋庄镇发展痛点分析矩阵', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('解决难度（分）', fontsize=12)
    plt.ylabel('影响程度（分）', fontsize=12)

# 设置坐标轴范围
plt.xlim(2, 10)
plt.ylim(6, 10)

# 添加网格
plt.grid(True, alpha=0.3, linestyle=':')

# 添加图例
plt.legend(prop=alibaba_font if alibaba_font else None, loc='lower right',
          frameon=True, fancybox=True, shadow=True)

# 设置布局
plt.tight_layout()

# 添加数据来源说明
plt.figtext(0.02, 0.02, '数据来源：宋庄镇实地调研与专家评估', fontsize=8, 
           fontproperties=alibaba_font if alibaba_font else None, alpha=0.7)

# 保存图表
output_path = os.path.join(output_dir, 'chart_02_散点图_01.png')
plt.savefig(output_path, dpi=300, bbox_inches='tight', 
           facecolor='white', edgecolor='none')
print(f"图表已保存至: {output_path}")

# 显示图表（可选）
# plt.show()

plt.close()
