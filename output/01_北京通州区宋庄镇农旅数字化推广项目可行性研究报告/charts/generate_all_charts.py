#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import subprocess
import sys

def run_chart_script(script_path):
    """运行单个图表生成脚本"""
    try:
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, text=True, cwd=os.path.dirname(script_path))
        if result.returncode == 0:
            print(f"✅ 成功生成: {os.path.basename(script_path)}")
            if result.stdout:
                print(f"   输出: {result.stdout.strip()}")
        else:
            print(f"❌ 生成失败: {os.path.basename(script_path)}")
            if result.stderr:
                print(f"   错误: {result.stderr.strip()}")
    except Exception as e:
        print(f"❌ 执行错误: {os.path.basename(script_path)} - {str(e)}")

def main():
    """批量生成所有图表"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 获取所有图表脚本文件
    chart_scripts = []
    for file in os.listdir(current_dir):
        if file.startswith('chart_') and file.endswith('.py') and file != 'generate_all_charts.py':
            chart_scripts.append(os.path.join(current_dir, file))
    
    # 按文件名排序
    chart_scripts.sort()
    
    print(f"🚀 开始批量生成 {len(chart_scripts)} 个图表...")
    print("=" * 50)
    
    success_count = 0
    for script in chart_scripts:
        run_chart_script(script)
        success_count += 1
    
    print("=" * 50)
    print(f"📊 图表生成完成！共处理 {len(chart_scripts)} 个图表脚本")
    
    # 检查生成的PNG文件
    png_files = [f for f in os.listdir(current_dir) if f.endswith('.png')]
    print(f"📁 生成的图表文件数量: {len(png_files)}")
    
    if png_files:
        print("生成的图表文件:")
        for png_file in sorted(png_files):
            print(f"   📈 {png_file}")

if __name__ == "__main__":
    main()
