#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
from matplotlib import font_manager
from matplotlib.patches import Rectangle, Circle
import matplotlib.patches as mpatches

# 查找阿里巴巴字体
font_list = font_manager.findSystemFonts(fontpaths=None, fontext='ttf')
alibaba_fonts = [f for f in font_list if 'Alibaba' in f]

# 创建字体对象
alibaba_font = None
if alibaba_fonts:
    try:
        alibaba_font = font_manager.FontProperties(fname=alibaba_fonts[0])
    except Exception as e:
        print(f"创建字体对象失败: {e}")

# 手动添加阿里巴巴字体
for font_path in alibaba_fonts:
    try:
        font_manager.fontManager.addfont(font_path)
    except Exception as e:
        print(f"添加字体失败: {font_path}, 错误: {e}")

# 设置字体
plt.rcParams['font.sans-serif'] = ['AlibabaPuHuiTi-3-55-Regular', 'AlibabaPuHuiTi-3-65-Medium', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 如果没有成功创建字体对象，尝试使用名称创建
if not alibaba_font:
    try:
        alibaba_font = font_manager.FontProperties(family='AlibabaPuHuiTi-3-55-Regular')
    except Exception as e:
        try:
            alibaba_font = font_manager.FontProperties(family='SimHei')
        except:
            print("无法创建任何中文字体对象，图表中文可能无法正确显示")

# 设置全局字体样式
plt.rcParams['font.family'] = 'sans-serif'

# 创建输出目录
output_dir = os.path.dirname(os.path.abspath(__file__))
os.makedirs(output_dir, exist_ok=True)

# 创建北京市农旅项目分布数据（简化地图）
districts = {
    '密云区': {'pos': (8, 9), 'projects': 45, 'level': 'high'},
    '怀柔区': {'pos': (6, 8), 'projects': 38, 'level': 'high'},
    '延庆区': {'pos': (3, 9), 'projects': 32, 'level': 'medium'},
    '平谷区': {'pos': (10, 6), 'projects': 28, 'level': 'medium'},
    '顺义区': {'pos': (8, 6), 'projects': 35, 'level': 'high'},
    '昌平区': {'pos': (5, 7), 'projects': 42, 'level': 'high'},
    '通州区': {'pos': (9, 4), 'projects': 25, 'level': 'medium'},
    '大兴区': {'pos': (6, 3), 'projects': 30, 'level': 'medium'},
    '房山区': {'pos': (4, 4), 'projects': 22, 'level': 'low'},
    '门头沟区': {'pos': (2, 6), 'projects': 18, 'level': 'low'},
    '石景山区': {'pos': (4, 5), 'projects': 8, 'level': 'low'},
    '海淀区': {'pos': (5, 5), 'projects': 15, 'level': 'low'},
    '朝阳区': {'pos': (7, 5), 'projects': 12, 'level': 'low'},
    '东城区': {'pos': (6, 5), 'projects': 5, 'level': 'low'},
    '西城区': {'pos': (5, 5), 'projects': 6, 'level': 'low'},
    '丰台区': {'pos': (5, 4), 'projects': 10, 'level': 'low'}
}

# 创建图表
fig, ax = plt.subplots(figsize=(12, 8))

# 定义颜色映射
color_map = {
    'high': '#FF6B6B',    # 高密度 - 红色
    'medium': '#4ECDC4',  # 中密度 - 青色
    'low': '#45B7D1'      # 低密度 - 蓝色
}

# 定义大小映射
def get_size(projects):
    return projects * 10  # 项目数量 * 10 作为圆圈大小

# 绘制区域和项目分布
for district, data in districts.items():
    x, y = data['pos']
    projects = data['projects']
    level = data['level']
    
    # 绘制圆圈表示项目密度
    circle = Circle((x, y), radius=np.sqrt(get_size(projects))/10, 
                   color=color_map[level], alpha=0.6, edgecolor='white', linewidth=2)
    ax.add_patch(circle)
    
    # 添加区域名称
    ax.text(x, y-0.8, district, ha='center', va='center', fontsize=9,
           fontweight='bold', fontproperties=alibaba_font if alibaba_font else None)
    
    # 添加项目数量
    ax.text(x, y, str(projects), ha='center', va='center', fontsize=10,
           fontweight='bold', color='white',
           fontproperties=alibaba_font if alibaba_font else None)

# 特别标注宋庄镇位置
songzhuang_x, songzhuang_y = 9.5, 4.5
star = plt.scatter(songzhuang_x, songzhuang_y, s=300, c='gold', marker='*', 
                  edgecolors='red', linewidth=2, zorder=10)
ax.text(songzhuang_x+0.3, songzhuang_y+0.3, '宋庄镇', ha='left', va='bottom', 
       fontsize=11, fontweight='bold', color='red',
       fontproperties=alibaba_font if alibaba_font else None,
       bbox=dict(boxstyle="round,pad=0.2", facecolor='yellow', alpha=0.7))

# 设置图表标题
if alibaba_font:
    ax.set_title('北京市农旅项目分布热力图', fontsize=16, fontweight='bold', 
                pad=20, fontproperties=alibaba_font)
else:
    ax.set_title('北京市农旅项目分布热力图', fontsize=16, fontweight='bold', pad=20)

# 设置坐标轴
ax.set_xlim(0, 12)
ax.set_ylim(1, 11)
ax.set_aspect('equal')

# 隐藏坐标轴
ax.axis('off')

# 添加图例
legend_elements = [
    mpatches.Circle((0, 0), 1, facecolor=color_map['high'], alpha=0.6, 
                   edgecolor='white', linewidth=2, label='高密度区域（30+项目）'),
    mpatches.Circle((0, 0), 1, facecolor=color_map['medium'], alpha=0.6, 
                   edgecolor='white', linewidth=2, label='中密度区域（20-30项目）'),
    mpatches.Circle((0, 0), 1, facecolor=color_map['low'], alpha=0.6, 
                   edgecolor='white', linewidth=2, label='低密度区域（<20项目）'),
    plt.Line2D([0], [0], marker='*', color='w', markerfacecolor='gold', 
              markersize=15, markeredgecolor='red', markeredgewidth=2, 
              label='宋庄镇位置')
]

ax.legend(handles=legend_elements, loc='upper left', 
         prop=alibaba_font if alibaba_font else None,
         frameon=True, fancybox=True, shadow=True)

# 添加说明文字
explanation = "圆圈大小表示项目数量，颜色表示项目密度等级"
ax.text(6, 0.5, explanation, ha='center', va='center', fontsize=10,
       style='italic', fontproperties=alibaba_font if alibaba_font else None,
       bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgray', alpha=0.5))

# 设置布局
plt.tight_layout()

# 添加数据来源说明
plt.figtext(0.02, 0.02, '数据来源：北京市农业农村局统计数据', fontsize=8, 
           fontproperties=alibaba_font if alibaba_font else None, alpha=0.7)

# 保存图表
output_path = os.path.join(output_dir, 'chart_02_地图_01.png')
plt.savefig(output_path, dpi=300, bbox_inches='tight', 
           facecolor='white', edgecolor='none')
print(f"图表已保存至: {output_path}")

# 显示图表（可选）
# plt.show()

plt.close()
