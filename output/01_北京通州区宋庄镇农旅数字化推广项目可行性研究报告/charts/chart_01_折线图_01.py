#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
from matplotlib import font_manager

# 查找阿里巴巴字体
font_list = font_manager.findSystemFonts(fontpaths=None, fontext='ttf')
alibaba_fonts = [f for f in font_list if 'Alibaba' in f]

# 创建字体对象
alibaba_font = None
if alibaba_fonts:
    try:
        alibaba_font = font_manager.FontProperties(fname=alibaba_fonts[0])
    except Exception as e:
        print(f"创建字体对象失败: {e}")

# 手动添加阿里巴巴字体
for font_path in alibaba_fonts:
    try:
        font_manager.fontManager.addfont(font_path)
    except Exception as e:
        print(f"添加字体失败: {font_path}, 错误: {e}")

# 设置字体
plt.rcParams['font.sans-serif'] = ['AlibabaPuHuiTi-3-55-Regular', 'AlibabaPuHuiTi-3-65-Medium', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 如果没有成功创建字体对象，尝试使用名称创建
if not alibaba_font:
    try:
        alibaba_font = font_manager.FontProperties(family='AlibabaPuHuiTi-3-55-Regular')
    except Exception as e:
        try:
            alibaba_font = font_manager.FontProperties(family='SimHei')
        except:
            print("无法创建任何中文字体对象，图表中文可能无法正确显示")

# 设置全局字体样式
plt.rcParams['font.family'] = 'sans-serif'

# 创建输出目录
output_dir = os.path.dirname(os.path.abspath(__file__))
os.makedirs(output_dir, exist_ok=True)

# 创建数字农旅市场规模增长趋势数据
years = ['2020', '2021', '2022', '2023', '2024', '2025']
digital_agriculture = [1800, 2100, 2500, 2950, 3450, 4000]  # 数字农业市场规模（亿元）
smart_tourism = [1200, 1450, 1750, 2100, 2500, 3000]       # 智慧旅游市场规模（亿元）
agri_tourism = [800, 1000, 1250, 1550, 1900, 2300]         # 农旅融合市场规模（亿元）

# 创建数据框
df = pd.DataFrame({
    '年份': years,
    '数字农业': digital_agriculture,
    '智慧旅游': smart_tourism,
    '农旅融合': agri_tourism
})

# 创建图表
plt.figure(figsize=(12, 8))

# 设置线条样式和颜色
colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
line_styles = ['-', '--', '-.']
markers = ['o', 's', '^']

# 绘制折线图
for i, column in enumerate(['数字农业', '智慧旅游', '农旅融合']):
    plt.plot(years, df[column], color=colors[i], linewidth=3, 
             linestyle=line_styles[i], marker=markers[i], markersize=8,
             label=column, alpha=0.8)
    
    # 添加数值标签
    for j, value in enumerate(df[column]):
        plt.annotate(f'{value}', (j, value), textcoords="offset points", 
                    xytext=(0,10), ha='center', fontsize=9,
                    fontproperties=alibaba_font if alibaba_font else None)

# 设置图表标题和标签
if alibaba_font:
    plt.title('数字农旅市场规模增长趋势（2020-2025年）', 
              fontsize=16, fontweight='bold', pad=20, fontproperties=alibaba_font)
    plt.xlabel('年份', fontsize=12, fontproperties=alibaba_font)
    plt.ylabel('市场规模（亿元）', fontsize=12, fontproperties=alibaba_font)
else:
    plt.title('数字农旅市场规模增长趋势（2020-2025年）', 
              fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('年份', fontsize=12)
    plt.ylabel('市场规模（亿元）', fontsize=12)

# 设置网格
plt.grid(True, alpha=0.3, linestyle='--')

# 添加图例
plt.legend(prop=alibaba_font if alibaba_font else None, loc='upper left', 
          frameon=True, fancybox=True, shadow=True)

# 设置y轴范围
plt.ylim(0, max(digital_agriculture) * 1.1)

# 添加趋势线背景色
plt.fill_between(years, 0, digital_agriculture, alpha=0.1, color=colors[0])
plt.fill_between(years, 0, smart_tourism, alpha=0.1, color=colors[1])
plt.fill_between(years, 0, agri_tourism, alpha=0.1, color=colors[2])

# 设置布局
plt.tight_layout()

# 添加数据来源说明
plt.figtext(0.02, 0.02, '数据来源：农业农村部、文化和旅游部相关统计数据', fontsize=8, 
           fontproperties=alibaba_font if alibaba_font else None, alpha=0.7)

# 保存图表
output_path = os.path.join(output_dir, 'chart_01_折线图_01.png')
plt.savefig(output_path, dpi=300, bbox_inches='tight', 
           facecolor='white', edgecolor='none')
print(f"图表已保存至: {output_path}")

# 显示图表（可选）
# plt.show()

plt.close()
