#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
from matplotlib import font_manager

# 查找阿里巴巴字体
font_list = font_manager.findSystemFonts(fontpaths=None, fontext='ttf')
alibaba_fonts = [f for f in font_list if 'Alibaba' in f]

# 创建字体对象
alibaba_font = None
if alibaba_fonts:
    try:
        alibaba_font = font_manager.FontProperties(fname=alibaba_fonts[0])
    except Exception as e:
        print(f"创建字体对象失败: {e}")

# 手动添加阿里巴巴字体
for font_path in alibaba_fonts:
    try:
        font_manager.fontManager.addfont(font_path)
    except Exception as e:
        print(f"添加字体失败: {font_path}, 错误: {e}")

# 设置字体
plt.rcParams['font.sans-serif'] = ['AlibabaPuHuiTi-3-55-Regular', 'AlibabaPuHuiTi-3-65-Medium', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 如果没有成功创建字体对象，尝试使用名称创建
if not alibaba_font:
    try:
        alibaba_font = font_manager.FontProperties(family='AlibabaPuHuiTi-3-55-Regular')
    except Exception as e:
        try:
            alibaba_font = font_manager.FontProperties(family='SimHei')
        except:
            print("无法创建任何中文字体对象，图表中文可能无法正确显示")

# 设置全局字体样式
plt.rcParams['font.family'] = 'sans-serif'

# 创建输出目录
output_dir = os.path.dirname(os.path.abspath(__file__))
os.makedirs(output_dir, exist_ok=True)

# 创建国家政策支持体系框架数据
categories = ['数字乡村战略', '乡村振兴战略', '文旅融合政策', '数字经济政策', 
              '农村电商政策', '人才培养政策', '基础设施政策', '财政支持政策']

# 政策支持强度评分（1-10分）
support_scores = [9.2, 9.5, 8.8, 9.0, 8.5, 8.2, 8.7, 8.9]

# 计算角度
N = len(categories)
angles = [n / float(N) * 2 * np.pi for n in range(N)]
angles += angles[:1]  # 闭合雷达图

# 数据也需要闭合
support_scores += support_scores[:1]

# 创建图表
fig, ax = plt.subplots(figsize=(12, 8), subplot_kw=dict(projection='polar'))

# 绘制雷达图
ax.plot(angles, support_scores, 'o-', linewidth=3, label='政策支持强度', color='#1f77b4')
ax.fill(angles, support_scores, alpha=0.25, color='#1f77b4')

# 添加数值标签
for angle, score in zip(angles[:-1], support_scores[:-1]):
    ax.text(angle, score + 0.3, f'{score}', ha='center', va='center', 
           fontsize=10, fontweight='bold', color='#1f77b4',
           fontproperties=alibaba_font if alibaba_font else None)

# 设置标签
ax.set_xticks(angles[:-1])
ax.set_xticklabels(categories, fontproperties=alibaba_font if alibaba_font else None)

# 设置y轴范围和标签
ax.set_ylim(0, 10)
ax.set_yticks([2, 4, 6, 8, 10])
ax.set_yticklabels(['2', '4', '6', '8', '10'], fontsize=9)

# 添加网格
ax.grid(True, alpha=0.3)

# 设置图表标题
if alibaba_font:
    plt.title('国家政策支持体系框架\n（政策支持强度评估）', 
              fontsize=16, fontweight='bold', pad=30, fontproperties=alibaba_font)
else:
    plt.title('国家政策支持体系框架\n（政策支持强度评估）', 
              fontsize=16, fontweight='bold', pad=30)

# 添加图例
plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0),
          prop=alibaba_font if alibaba_font else None)

# 设置布局
plt.tight_layout()

# 添加数据来源说明
plt.figtext(0.02, 0.02, '数据来源：国家相关政策文件分析', fontsize=8, 
           fontproperties=alibaba_font if alibaba_font else None, alpha=0.7)

# 保存图表
output_path = os.path.join(output_dir, 'chart_02_雷达图_01.png')
plt.savefig(output_path, dpi=300, bbox_inches='tight', 
           facecolor='white', edgecolor='none')
print(f"图表已保存至: {output_path}")

# 显示图表（可选）
# plt.show()

plt.close()
