#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
from matplotlib import font_manager

# 查找阿里巴巴字体
font_list = font_manager.findSystemFonts(fontpaths=None, fontext='ttf')
alibaba_fonts = [f for f in font_list if 'Alibaba' in f]

# 创建字体对象
alibaba_font = None
if alibaba_fonts:
    try:
        alibaba_font = font_manager.FontProperties(fname=alibaba_fonts[0])
    except Exception as e:
        print(f"创建字体对象失败: {e}")

# 手动添加阿里巴巴字体
for font_path in alibaba_fonts:
    try:
        font_manager.fontManager.addfont(font_path)
    except Exception as e:
        print(f"添加字体失败: {font_path}, 错误: {e}")

# 设置字体
plt.rcParams['font.sans-serif'] = ['AlibabaPuHuiTi-3-55-Regular', 'AlibabaPuHuiTi-3-65-Medium', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 如果没有成功创建字体对象，尝试使用名称创建
if not alibaba_font:
    try:
        alibaba_font = font_manager.FontProperties(family='AlibabaPuHuiTi-3-55-Regular')
    except Exception as e:
        try:
            alibaba_font = font_manager.FontProperties(family='SimHei')
        except:
            print("无法创建任何中文字体对象，图表中文可能无法正确显示")

# 设置全局字体样式
plt.rcParams['font.family'] = 'sans-serif'

# 创建输出目录
output_dir = os.path.dirname(os.path.abspath(__file__))
os.makedirs(output_dir, exist_ok=True)

# 设置政府报告专业配色方案
colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']

# 创建宋庄镇农旅发展现状对比分析数据
categories = ['农业产值', '艺术产业产值', '旅游收入', '数字化水平', '基础设施', '人才储备']
current_values = [65, 85, 45, 35, 70, 55]  # 当前水平（百分制）
target_values = [85, 95, 80, 75, 85, 80]   # 目标水平（百分制）

# 创建数据框
df = pd.DataFrame({
    '指标': categories,
    '现状水平': current_values,
    '目标水平': target_values
})

# 创建图表
plt.figure(figsize=(12, 8))

# 设置柱状图位置
x = np.arange(len(categories))
width = 0.35

# 绘制柱状图
bars1 = plt.bar(x - width/2, current_values, width, label='现状水平', 
                color=colors[0], alpha=0.8, edgecolor='white', linewidth=1)
bars2 = plt.bar(x + width/2, target_values, width, label='目标水平', 
                color=colors[1], alpha=0.8, edgecolor='white', linewidth=1)

# 添加数值标签
for i, (bar1, bar2) in enumerate(zip(bars1, bars2)):
    height1 = bar1.get_height()
    height2 = bar2.get_height()
    plt.text(bar1.get_x() + bar1.get_width()/2., height1 + 1,
             f'{height1}', ha='center', va='bottom', fontsize=10,
             fontproperties=alibaba_font if alibaba_font else None)
    plt.text(bar2.get_x() + bar2.get_width()/2., height2 + 1,
             f'{height2}', ha='center', va='bottom', fontsize=10,
             fontproperties=alibaba_font if alibaba_font else None)

# 设置图表标题和标签
if alibaba_font:
    plt.title('宋庄镇农旅发展现状对比分析', fontsize=16, fontweight='bold', 
              pad=20, fontproperties=alibaba_font)
    plt.xlabel('发展指标', fontsize=12, fontproperties=alibaba_font)
    plt.ylabel('发展水平（分）', fontsize=12, fontproperties=alibaba_font)
else:
    plt.title('宋庄镇农旅发展现状对比分析', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('发展指标', fontsize=12)
    plt.ylabel('发展水平（分）', fontsize=12)

# 设置x轴标签
plt.xticks(x, categories, fontproperties=alibaba_font if alibaba_font else None)

# 设置y轴范围和网格
plt.ylim(0, 100)
plt.grid(axis='y', alpha=0.3, linestyle='--')

# 添加图例
plt.legend(prop=alibaba_font if alibaba_font else None, loc='upper left')

# 设置布局
plt.tight_layout()

# 添加数据来源说明
plt.figtext(0.02, 0.02, '数据来源：宋庄镇发展现状调研', fontsize=8, 
           fontproperties=alibaba_font if alibaba_font else None, alpha=0.7)

# 保存图表
output_path = os.path.join(output_dir, 'chart_01_柱状图_01.png')
plt.savefig(output_path, dpi=300, bbox_inches='tight', 
           facecolor='white', edgecolor='none')
print(f"图表已保存至: {output_path}")

# 显示图表（可选）
# plt.show()

plt.close()
