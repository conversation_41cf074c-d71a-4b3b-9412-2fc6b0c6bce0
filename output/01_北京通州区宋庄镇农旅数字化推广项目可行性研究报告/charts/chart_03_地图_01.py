#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
from matplotlib import font_manager
from matplotlib.patches import Rectangle, Circle, FancyBboxPatch
import matplotlib.patches as mpatches

# 查找阿里巴巴字体
font_list = font_manager.findSystemFonts(fontpaths=None, fontext='ttf')
alibaba_fonts = [f for f in font_list if 'Alibaba' in f]

# 创建字体对象
alibaba_font = None
if alibaba_fonts:
    try:
        alibaba_font = font_manager.FontProperties(fname=alibaba_fonts[0])
    except Exception as e:
        print(f"创建字体对象失败: {e}")

# 手动添加阿里巴巴字体
for font_path in alibaba_fonts:
    try:
        font_manager.fontManager.addfont(font_path)
    except Exception as e:
        print(f"添加字体失败: {font_path}, 错误: {e}")

# 设置字体
plt.rcParams['font.sans-serif'] = ['AlibabaPuHuiTi-3-55-Regular', 'AlibabaPuHuiTi-3-65-Medium', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 如果没有成功创建字体对象，尝试使用名称创建
if not alibaba_font:
    try:
        alibaba_font = font_manager.FontProperties(family='AlibabaPuHuiTi-3-55-Regular')
    except Exception as e:
        try:
            alibaba_font = font_manager.FontProperties(family='SimHei')
        except:
            print("无法创建任何中文字体对象，图表中文可能无法正确显示")

# 设置全局字体样式
plt.rcParams['font.family'] = 'sans-serif'

# 创建输出目录
output_dir = os.path.dirname(os.path.abspath(__file__))
os.makedirs(output_dir, exist_ok=True)

# 创建图表
fig, ax = plt.subplots(figsize=(12, 8))

# 绘制北京城区（简化）
beijing_center = Rectangle((4, 4), 4, 3, facecolor='lightgray', alpha=0.5, 
                          edgecolor='black', linewidth=2)
ax.add_patch(beijing_center)
ax.text(6, 5.5, '北京城区', ha='center', va='center', fontsize=14, fontweight='bold',
       fontproperties=alibaba_font if alibaba_font else None)

# 绘制宋庄镇位置
songzhuang = Circle((10, 3), 0.8, facecolor='#FF6B6B', alpha=0.8, 
                   edgecolor='white', linewidth=3)
ax.add_patch(songzhuang)
ax.text(10, 3, '宋庄镇', ha='center', va='center', fontsize=12, fontweight='bold',
       color='white', fontproperties=alibaba_font if alibaba_font else None)

# 绘制交通线路
# 京哈高速
ax.plot([8, 11], [5, 3.5], 'r-', linewidth=4, alpha=0.7, label='京哈高速')
ax.text(9.5, 4.5, '京哈高速', fontsize=10, rotation=-25, 
       fontproperties=alibaba_font if alibaba_font else None,
       bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))

# 六环路
circle_6ring = Circle((6, 5.5), 3, fill=False, edgecolor='blue', 
                     linewidth=3, alpha=0.7, linestyle='--')
ax.add_patch(circle_6ring)
ax.text(9, 7.5, '六环路', fontsize=10, color='blue', fontweight='bold',
       fontproperties=alibaba_font if alibaba_font else None)

# 京通快速路
ax.plot([8, 10.5], [5.5, 3.2], 'g-', linewidth=3, alpha=0.7, label='京通快速路')
ax.text(9, 3.8, '京通快速路', fontsize=10, rotation=-35, color='green',
       fontproperties=alibaba_font if alibaba_font else None,
       bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))

# 添加距离标注
ax.annotate('', xy=(10, 3), xytext=(6, 5.5), 
           arrowprops=dict(arrowstyle='<->', color='purple', lw=2))
ax.text(8, 4.5, '约25公里\n车程40分钟', ha='center', va='center', fontsize=11,
       color='purple', fontweight='bold', fontproperties=alibaba_font if alibaba_font else None,
       bbox=dict(boxstyle="round,pad=0.3", facecolor='lavender', alpha=0.8))

# 添加周边重要地点
locations = [
    {'name': '通州城区', 'pos': (9, 4.5), 'color': '#4ECDC4'},
    {'name': '首都机场', 'pos': (8, 7), 'color': '#45B7D1'},
    {'name': '大兴机场', 'pos': (5, 2), 'color': '#45B7D1'},
    {'name': '环球影城', 'pos': (9.5, 4), 'color': '#96CEB4'}
]

for loc in locations:
    circle = Circle(loc['pos'], 0.3, facecolor=loc['color'], alpha=0.7, 
                   edgecolor='white', linewidth=1)
    ax.add_patch(circle)
    ax.text(loc['pos'][0], loc['pos'][1]-0.6, loc['name'], ha='center', va='top', 
           fontsize=9, fontproperties=alibaba_font if alibaba_font else None)

# 添加交通优势说明框
advantage_text = "交通优势：\n• 距北京城区25公里\n• 多条高速公路直达\n• 公共交通便利\n• 未来地铁规划覆盖"
ax.text(1, 7, advantage_text, fontsize=10, va='top',
       fontproperties=alibaba_font if alibaba_font else None,
       bbox=dict(boxstyle="round,pad=0.5", facecolor='lightyellow', alpha=0.8))

# 设置图表标题
if alibaba_font:
    ax.set_title('宋庄镇地理位置与交通优势', fontsize=16, fontweight='bold', 
                pad=20, fontproperties=alibaba_font)
else:
    ax.set_title('宋庄镇地理位置与交通优势', fontsize=16, fontweight='bold', pad=20)

# 设置坐标轴
ax.set_xlim(0, 12)
ax.set_ylim(1, 9)
ax.set_aspect('equal')

# 隐藏坐标轴
ax.axis('off')

# 添加图例
legend_elements = [
    mpatches.Circle((0, 0), 1, facecolor='#FF6B6B', alpha=0.8, 
                   edgecolor='white', linewidth=2, label='宋庄镇'),
    mpatches.Rectangle((0, 0), 1, 1, facecolor='lightgray', alpha=0.5, 
                      edgecolor='black', linewidth=2, label='北京城区'),
    plt.Line2D([0], [0], color='red', linewidth=4, alpha=0.7, label='高速公路'),
    plt.Line2D([0], [0], color='blue', linewidth=3, alpha=0.7, 
              linestyle='--', label='环路'),
    mpatches.Circle((0, 0), 1, facecolor='#4ECDC4', alpha=0.7, 
                   edgecolor='white', linewidth=1, label='重要地点')
]

ax.legend(handles=legend_elements, loc='upper right', 
         prop=alibaba_font if alibaba_font else None,
         frameon=True, fancybox=True, shadow=True)

# 设置布局
plt.tight_layout()

# 添加数据来源说明
plt.figtext(0.02, 0.02, '数据来源：北京市交通委员会、地理信息系统', fontsize=8, 
           fontproperties=alibaba_font if alibaba_font else None, alpha=0.7)

# 保存图表
output_path = os.path.join(output_dir, 'chart_03_地图_01.png')
plt.savefig(output_path, dpi=300, bbox_inches='tight', 
           facecolor='white', edgecolor='none')
print(f"图表已保存至: {output_path}")

# 显示图表（可选）
# plt.show()

plt.close()
