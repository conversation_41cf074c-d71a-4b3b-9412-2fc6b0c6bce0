#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import os
from matplotlib import font_manager

# 查找阿里巴巴字体
font_list = font_manager.findSystemFonts(fontpaths=None, fontext='ttf')
alibaba_fonts = [f for f in font_list if 'Alibaba' in f]

# 创建字体对象
alibaba_font = None
if alibaba_fonts:
    try:
        alibaba_font = font_manager.FontProperties(fname=alibaba_fonts[0])
    except Exception as e:
        print(f"创建字体对象失败: {e}")

# 手动添加阿里巴巴字体
for font_path in alibaba_fonts:
    try:
        font_manager.fontManager.addfont(font_path)
    except Exception as e:
        print(f"添加字体失败: {font_path}, 错误: {e}")

# 设置字体
plt.rcParams['font.sans-serif'] = ['AlibabaPuHuiTi-3-55-Regular', 'AlibabaPuHuiTi-3-65-Medium', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 如果没有成功创建字体对象，尝试使用名称创建
if not alibaba_font:
    try:
        alibaba_font = font_manager.FontProperties(family='AlibabaPuHuiTi-3-55-Regular')
    except Exception as e:
        try:
            alibaba_font = font_manager.FontProperties(family='SimHei')
        except:
            print("无法创建任何中文字体对象，图表中文可能无法正确显示")

# 设置全局字体样式
plt.rcParams['font.family'] = 'sans-serif'

# 创建输出目录
output_dir = os.path.dirname(os.path.abspath(__file__))
os.makedirs(output_dir, exist_ok=True)

# 创建图表
fig, ax = plt.subplots(figsize=(12, 8))

# 定义流程节点
nodes = [
    {'name': '产业目标\n建成3-5个特色\n数字农旅项目', 'pos': (2, 7), 'color': '#FF6B6B'},
    {'name': '市场目标\n年接待游客50万人次\n城区游客占比70%', 'pos': (6, 7), 'color': '#4ECDC4'},
    {'name': '经济目标\n直接经济效益3000万元\n农民人均增收3000元', 'pos': (10, 7), 'color': '#45B7D1'},
    {'name': '社会目标\n创造就业岗位300个\n培训农民500人次', 'pos': (2, 4), 'color': '#96CEB4'},
    {'name': '示范目标\n形成可复制推广\n的发展模式', 'pos': (6, 4), 'color': '#FFEAA7'},
    {'name': '核心策略\n艺农融合\n数字赋能', 'pos': (6, 1), 'color': '#DDA0DD'}
]

# 定义连接关系
connections = [
    (0, 1), (1, 2), (2, 3), (3, 4), (4, 5),
    (0, 3), (1, 4), (2, 5)
]

# 绘制节点
for node in nodes:
    # 创建圆角矩形
    box = FancyBboxPatch((node['pos'][0]-0.8, node['pos'][1]-0.6), 1.6, 1.2,
                        boxstyle="round,pad=0.1", facecolor=node['color'],
                        edgecolor='white', linewidth=2, alpha=0.8)
    ax.add_patch(box)
    
    # 添加文字
    ax.text(node['pos'][0], node['pos'][1], node['name'], 
           ha='center', va='center', fontsize=10, fontweight='bold',
           color='white', fontproperties=alibaba_font if alibaba_font else None)

# 绘制连接线
for start_idx, end_idx in connections:
    start_pos = nodes[start_idx]['pos']
    end_pos = nodes[end_idx]['pos']
    
    # 计算箭头位置
    dx = end_pos[0] - start_pos[0]
    dy = end_pos[1] - start_pos[1]
    
    # 调整起点和终点位置，避免与节点重叠
    if dx != 0:
        start_x = start_pos[0] + (0.8 if dx > 0 else -0.8)
        end_x = end_pos[0] + (-0.8 if dx > 0 else 0.8)
    else:
        start_x = start_pos[0]
        end_x = end_pos[0]
    
    if dy != 0:
        start_y = start_pos[1] + (-0.6 if dy < 0 else 0.6)
        end_y = end_pos[1] + (0.6 if dy < 0 else -0.6)
    else:
        start_y = start_pos[1]
        end_y = end_pos[1]
    
    # 绘制箭头
    arrow = patches.FancyArrowPatch((start_x, start_y), (end_x, end_y),
                                   connectionstyle="arc3,rad=0.1",
                                   arrowstyle='->', mutation_scale=20,
                                   color='#666666', linewidth=2, alpha=0.7)
    ax.add_patch(arrow)

# 设置图表标题
if alibaba_font:
    ax.set_title('项目目标达成路径图', fontsize=16, fontweight='bold', 
                pad=20, fontproperties=alibaba_font)
else:
    ax.set_title('项目目标达成路径图', fontsize=16, fontweight='bold', pad=20)

# 设置坐标轴
ax.set_xlim(0, 12)
ax.set_ylim(0, 8.5)
ax.set_aspect('equal')

# 隐藏坐标轴
ax.axis('off')

# 添加图例说明
legend_text = "目标实现路径：产业目标→市场目标→经济目标→社会目标→示范目标"
ax.text(6, 0.3, legend_text, ha='center', va='center', fontsize=10,
       style='italic', fontproperties=alibaba_font if alibaba_font else None,
       bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgray', alpha=0.5))

# 设置布局
plt.tight_layout()

# 添加数据来源说明
plt.figtext(0.02, 0.02, '数据来源：项目规划设计', fontsize=8, 
           fontproperties=alibaba_font if alibaba_font else None, alpha=0.7)

# 保存图表
output_path = os.path.join(output_dir, 'chart_01_流程图_01.png')
plt.savefig(output_path, dpi=300, bbox_inches='tight', 
           facecolor='white', edgecolor='none')
print(f"图表已保存至: {output_path}")

# 显示图表（可选）
# plt.show()

plt.close()
