#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import matplotlib.pyplot as plt
import os
from matplotlib import font_manager

# 简化字体设置
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 基础设施完善度评估数据
categories = ['交通条件', '通信网络', '水电设施', '公共服务', '安全保障', '环保设施']
scores = [7.5, 6.8, 8.2, 6.5, 7.8, 7.0]

# 计算角度
N = len(categories)
angles = [n / float(N) * 2 * np.pi for n in range(N)]
angles += angles[:1]
scores += scores[:1]

# 创建图表
fig, ax = plt.subplots(figsize=(12, 8), subplot_kw=dict(projection='polar'))
ax.plot(angles, scores, 'o-', linewidth=3, color='#1f77b4')
ax.fill(angles, scores, alpha=0.25, color='#1f77b4')

# 设置标签和范围
ax.set_xticks(angles[:-1])
ax.set_xticklabels(categories)
ax.set_ylim(0, 10)
ax.set_yticks([2, 4, 6, 8, 10])
ax.grid(True, alpha=0.3)

plt.title('基础设施完善度评估雷达图', fontsize=16, fontweight='bold', pad=30)
plt.tight_layout()

output_dir = os.path.dirname(os.path.abspath(__file__))
output_path = os.path.join(output_dir, 'chart_03_雷达图_01.png')
plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
print(f"图表已保存至: {output_path}")
plt.close()
